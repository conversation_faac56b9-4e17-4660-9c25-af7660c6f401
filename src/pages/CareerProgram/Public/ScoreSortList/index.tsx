import React, { Component } from 'react';
import { PageHeaderWrapper } from '@ant-design/pro-layout';
import SelectProvinceBar from './components/SelectProvieceBar';
import AdmissionDrawer from './components/AdmissionDrawer';
import IconButton from '@/components/IconButton';
import UploadButton from '@/components/UploadButton';
import ConfirmModal from './components/ConfimModal';
import { Table, Button, Popconfirm, message, Spin } from 'antd';
import api from '@/services/public';
import style from './index.less';
import { getUrlAllParams } from '@/utils/utils';

interface PageState {
  list: object[],
  pageIndex: number,
  pageSize: number,
  total: number,
  drawVisible: boolean,
  confirmModalVisible: boolean,
  filterData: any,
  uploadBack: any,
  uploadSource: any,
  pertainSubject: string,
  provinceCode: number | null,
  enrollYear: number | null,
  editeId: number,
  loading: boolean,
  importLoading: boolean,
  pointStart: number
}

export default class AdmissionListPage extends Component<object, PageState> {
  constructor(props: object) {
    super(props);
    const params: any = getUrlAllParams();
    this.state = {
      list: [],              // 列表数据
      filterData: null,        // 筛选信息
      provinceCode: null,       // 筛选省份
      pointStart: 0,
      enrollYear: null,         // 筛选年份
      editeId: 0,            // 编辑ID（0:创建）
      uploadBack: null,      // 上传回调资源
      uploadSource: {},      // 上传项目
      pertainSubject: '',    // 科类
      drawVisible: false,
      confirmModalVisible: false,
      pageIndex: (params && params.pageIndex) ? Number(params.pageIndex) : 1,
      pageSize: 10,
      total: 0,
      loading: false,
      importLoading: false
    };
  }


  componentDidMount() {
    this.getProvinceAndYears();
  }


  getProvinceAndYears = async () => {
    try {
      const { data } = await api.getProvinceAndYears({
        yearStart: 2017,
        examinationMode: 0
      });
      this.setState({
        filterData: data || null,
        provinceCode: (data && data.provinceList && data.provinceList.length>0)?data.provinceList[0].provinceCode : '',
        enrollYear: (data && data.yearList && data.yearList.length>0)?data.yearList[0].value : '',
      },() => {
        this.scoreSortList();
      })
    } catch (e) {
      console.log('getPlanFilterInfo', e);
    }
  }

  scoreSortList = async () => {
    try {
      const { provinceCode, enrollYear,pertainSubject, pageIndex, pageSize, pointStart } = this.state;
      this.setState({
        loading: true,
      })
      const { data: { data, totalRecords } } = await api.scoreSortList({
        provinceCode,
        historyYear: enrollYear,
        pertainSubject,
        pointStart,
        pageIndex,
        pageSize,
      });
      this.setState({
        loading: false,
        list: data || [],
        total: totalRecords
      })
    } catch (e) {
      console.log('getPlanFilterInfo', e);
    }
  }

  onDelete = async (val: any) => {
    try {
      await api.scoreSortDelete({
        ids: [val.id]
      }).then(response => {
        if (response.success) {
          const { list, pageIndex } = this.state;
          this.setState({
            pageIndex: (list.length === 1 && pageIndex > 1) ? pageIndex - 1 : pageIndex
          }, () => {
            this.scoreSortList()
          })

          message.success('删除成功！');
        }
      });
    } catch (e) {
      message.error('删除失败！');
    }
  }

  admissionUpdate = async (val: any) => {
    try {
      await api.admissionUpdate({ ...val }).then(response => {
        if (response.success) {
          message.success('修改成功！');
          this.scoreSortList();
        }
      });
    } catch (e) {
      message.error('修改失败！');
    }
  }

  scoreSortModify = async (val: any) => {
    const { id } = val;
    try {
      await api.scoreSortModify({ ...val }).then(response => {
        if (response.success) {
          message.success((Number(id) > 0)?'修改成功！':'创建成功！');
          this.setState({
            pageIndex: 1
          }, () => {
            this.scoreSortList();
          })
        }
      });
    } catch (e) {
      message.error('创建失败！');
    }
  }

  scoreSortBatchDelete = async () => {
    const { provinceCode, pertainSubject,enrollYear } = this.state;
    try {
      await api.scoreSortBatchDelete({
        pertainSubject,
        provinceCode,
        historyYear: enrollYear,
      }).then(response => {
        if (response.success) {
          message.success('删除成功！');
          this.setState({
            pageIndex: 1
          }, () => {
            this.scoreSortList();
          })
        }
      });
    } catch (e) {
      message.error('创建失败！');
    }
  }


  onSelect = (val: any) => {
    this.setState({ ...val, pageIndex: 1 }, () => {
      this.scoreSortList();
    });
  }


  onPageChange = (pageIndex: number, pageSize: number) => {
    this.setState({
      pageIndex,
      pageSize
    }, () => {
      this.scoreSortList();
    })
  }

  onSubmite = (save: boolean, val: any) => {
    if (save) {
      this.scoreSortModify(val);
    }
    this.setState({ drawVisible: false });
  }


  onCreate = () => {
    this.setState({ drawVisible: true, editeId: 0 });
  }

  onEdite = (val: any) => {
    this.setState({ drawVisible: true, editeId: val.id });
  }

  downTemplate = () => {
    window.open(`//file.ewt360.com/file/1146327991073144931`, '_blank')
  }


  onExcelSelect = async (file: any, para: any) => {
    const formData = new FormData();
    formData.append('file', file);
    this.setState({
      importLoading: true
    })
    await api.sortListExportIn(formData).then((response) => {
      this.setState({
        importLoading: false
      })
      if (response.success) {
        this.setState({
          uploadBack: response.data || null,
          uploadSource: para
        }, () => {
          this.setState({ confirmModalVisible: true });
        });
        this.scoreSortList();
      } else {
        message.error(response.msg);
      }
    });
  }

  onCancel = () => {
    this.setState({
      confirmModalVisible: false,
      uploadBack: null,
      uploadSource: {}
    });
    this.scoreSortList();
  }


  onConfirm = () => {
    const { uploadBack } = this.state;
    if (uploadBack.failFileKey) {
      try {
        // 导出操作
        const { location: local } = window;
        const exportUrl = `${local.protocol}//${local.host}/api/volunteermanage/manage/common/exportFailedData?failFileKey=${uploadBack.failFileKey}`;
        window.open(exportUrl, '_blank')
      } catch (e) {
        message.error('导出异常～');
      }
    }
    this.setState({ confirmModalVisible: false, uploadBack: null, uploadSource: {} });
  }

    onExport = () => {
    try {
      // 导出操作
      const { provinceCode, enrollYear, pertainSubject } = this.state;
      const { location: local } = window;
      let exportUrl = `${local.protocol}//${local.host}/api/volunteermanage/manage/common/pointPosition/export?provinceCode=${provinceCode}&historyYear=${enrollYear}`;
      if(pertainSubject){
        exportUrl = `${local.protocol}//${local.host}/api/volunteermanage/manage/common/pointPosition/export?provinceCode=${provinceCode}&historyYear=${enrollYear}&pertainSubject=${pertainSubject}`;
      }
      window.open(exportUrl, '_blank')
    } catch (e) {
      message.error('导出异常～');
    }
  }

  render() {
    const { loading, provinceCode, enrollYear, list, filterData, pageIndex, pageSize, total, editeId, drawVisible, confirmModalVisible, uploadBack, importLoading } = this.state;
    const selectData = filterData ? filterData : null;
    const columns: any = [
      {
        title: 'ID',
        dataIndex: 'id',
        key: 'id',
        align: 'center',
      },
      {
        title: '省份',
        dataIndex: 'province',
        key: 'province',
        align: 'center',
      },
      {
        title: '年份',
        dataIndex: 'historyYear',
        key: 'historyYear',
        align: 'center'
      },
      {
        title: '科类',
        dataIndex: 'pertainSubjectName',
        key: 'pertainSubjectName',
        align: 'center'
      },
      {
        title: '层次',
        dataIndex: 'majorLevelName',
        key: 'majorLevelName',
        align: 'center'
      },


      {
        title: '分数',
        dataIndex: 'pointStart',
        key: 'pointStart',
        align: 'center'
      },
      {
        title: '最大分数',
        dataIndex: 'pointEnd',
        key: 'pointEnd',
        align: 'center'
      },
      {
        title: '人数',
        dataIndex: 'peopleNum',
        key: 'peopleNum',
        align: 'center'
      },
      {
        title: '位次',
        dataIndex: 'pointPosition',
        key: 'pointPosition',
        align: 'center'
      },
      {
        title: '备注',
        dataIndex: 'remark',
        key: 'remark',
        width: 200,
      },
      {
        title: '操作',
        dataIndex: 'operate',
        key: 'operate',
        align: 'center',
        render: (val: any, record: any) => {
          return <div>
            <Button type="link" onClick={() => this.onEdite(record)}>修改</Button>
            <Popconfirm
              title="此删除会导致数据丢失，确定执行此操作吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={() => this.onDelete(record)}>
              <Button danger type="link">删除</Button>
            </Popconfirm>
          </div>
        }
      }
    ];

    const pagination = {
      total,
      pageSize,
      onChange: this.onPageChange,
      pageSizeOptions: [10, 20, 50],
      current: pageIndex,
      showTotal: (total: any) => <span>{`共 ${total} 条数据`}</span>,
    };

    return <PageHeaderWrapper>
      <Spin spinning={importLoading} size="large" tip="数据导入中...">
        <div className="customPage">
          <div className="header">
            <SelectProvinceBar
              provinceCode={Number(provinceCode)}
              enrollYear={Number(enrollYear)}
              onSelect={this.onSelect}
              filterData={selectData}
            />
            <div className={style.action_wrapper}>
              <UploadButton
                type="primary"
                size="middle"
                onSuccess={this.onExcelSelect}
                style={{display:'inline-block',width:'82px'}}
                title="导入"
                otherParams={{}}
                fileType={['.xlsx']}
                isNotUpload
              />
              <IconButton
                iconType={3}
                type="text"
                style={{ color: "#4B80FF", fontSize: "12px" }}
                onClick={() => this.downTemplate()}
              >
                下载导入模板
              </IconButton>
              <Button type="primary" onClick={this.onCreate} style={{float:'right'}}>新增数据</Button>
              <Button type="primary" style={{float:'right',marginRight:'20px'}} onClick={() => this.onExport()}>导出筛选结果</Button>
              <Popconfirm
              title="此删除会导致数据丢失，确定执行此操作吗？"
              okText="确定"
              cancelText="取消"
              onConfirm={this.scoreSortBatchDelete}>
              <Button type="primary" style={{float:'right',marginRight:'20px'}}>批量删除</Button>
            </Popconfirm>
            </div>
          </div>
          <div className="content">
            <Table
              dataSource={list}
              loading={loading}
              columns={columns}
              pagination={pagination}
              rowKey={'id'}
              bordered
            />
            {drawVisible &&
              <AdmissionDrawer
                visible={drawVisible}
                onSubmite={this.onSubmite}
                filterData={filterData}
                Id={editeId}
              />
            }
            {confirmModalVisible &&
              <ConfirmModal
                visible={confirmModalVisible}
                title={'导入结果'}
                okText={uploadBack.failFileKey ? '导出失败记录' : '确定'}
                onCancel={this.onCancel}
                onConfirm={this.onConfirm}
                data={uploadBack}
              />
            }
          </div>
        </div>
      </Spin>
    </PageHeaderWrapper>
  }
}


